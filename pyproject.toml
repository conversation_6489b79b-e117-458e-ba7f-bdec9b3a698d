[project]
name = "imgsearch"
version = "0.1.1"
description = "ImgSearch is a lightweight image search engine that supports searching images by image."
readme = "README.md"
authors = [
    { name = "Seamile", email = "<EMAIL>" }
]
classifiers = [
    "Development Status :: 4 - Beta",
    "License :: OSI Approved :: MIT License",
    "Operating System :: MacOS",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: SCIENCE :: Artificial Intelligence :: Image Recognition",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Utilities",
]
requires-python = ">=3.11"
dependencies = [
    "numpy>=1.26.0",        # dependi: disable-check
    "torch>=2.2.0",         # dependi: disable-check
    "torchvision>=0.17.0",  # dependi: disable-check
    "bidict>=0.23.1",
    "hnswlib>=0.8.0",
    "msgpack>=1.1.1",
    "pillow>=11.3.0",
    "psutil>=7.0.0",
    "pyro5>=5.15",
    "transformers>=4.56.0",
]

[dependency-groups]
dev = [
    "ipython>=9.5.0",
    "pytest>=8.4.1",
]

[project.scripts]
isearch = "imgsearch.client:main"

[build-system]
requires = ["uv_build>=0.8.6,<0.9.0"]
build-backend = "uv_build"
