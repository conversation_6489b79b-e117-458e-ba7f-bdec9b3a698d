"""
System service installer for isearch.

This module provides functionality to install and uninstall isearch as a system service
on Linux (systemd) and macOS (launchd).
"""

import os
import platform
import shutil
import subprocess
import sys
from pathlib import Path

from imgsearch.consts import get_base_dir
from imgsearch.utils import colorize, print_err, print_warn


class ServiceInstaller:
    """Handles system service installation and management."""

    def __init__(self):
        self.system = platform.system().lower()
        self.is_root = os.geteuid() == 0
        self.base_dir = get_base_dir()

    def detect_system_support(self) -> tuple[bool, str]:
        """
        Detect if the current system supports service installation.

        Returns:
            tuple: (is_supported, error_message)
        """
        if self.system == 'linux':
            return self._detect_systemd()
        elif self.system == 'darwin':  # macOS
            return self._detect_launchd()
        else:
            return False, f'Unsupported operating system: {self.system}'

    def _detect_systemd(self) -> tuple[bool, str]:
        """Detect if systemd is available on Linux."""
        # Check if systemctl command exists
        if not shutil.which('systemctl'):
            return False, 'systemctl command not found. systemd is required for Linux service installation.'

        # Check if systemd is running
        try:
            result = subprocess.run(['systemctl', '--version'], capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                return False, 'systemd is not running or not available.'
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False, 'Failed to check systemd status.'

        # Check if we can write to system service directory
        system_service_dir = Path('/etc/systemd/system')
        if not system_service_dir.exists():
            return False, f'System service directory {system_service_dir} does not exist.'

        return True, ''

    def _detect_launchd(self) -> tuple[bool, str]:
        """Detect if launchd is available on macOS."""
        # Check if launchctl command exists
        if not shutil.which('launchctl'):
            return False, 'launchctl command not found. This should not happen on macOS.'

        # Check user launch agents directory
        user_agents_dir = Path.home() / 'Library' / 'LaunchAgents'
        try:
            user_agents_dir.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            return False, f'Cannot create or access {user_agents_dir}. Permission denied.'

        return True, ''

    def get_service_file_path(self) -> Path | None:
        """Get the path where the service file should be installed."""
        if self.system == 'linux':
            return Path('/etc/systemd/system/isearch.service')
        elif self.system == 'darwin':
            return Path.home() / 'Library' / 'LaunchAgents' / 'com.isearch.service.plist'
        return None

    def get_python_executable(self) -> str:
        """Get the absolute path to the current Python executable."""
        return sys.executable

    def get_isearch_module_path(self) -> str:
        """Get the path to run isearch as a module."""
        # Use python -m imgsearch instead of direct script path for better compatibility
        return f'{self.get_python_executable()} -m imgsearch'

    def check_prerequisites(self) -> tuple[bool, list[str]]:
        """
        Check all prerequisites for service installation.

        Returns:
            tuple: (all_ok, list_of_issues)
        """
        issues = []

        # Check system support
        supported, error_msg = self.detect_system_support()
        if not supported:
            issues.append(error_msg)

        # Check Python executable
        python_exe = self.get_python_executable()
        if not Path(python_exe).exists():
            issues.append(f'Python executable not found: {python_exe}')

        # Check if isearch module is importable
        try:
            import imgsearch  # noqa: F401
        except ImportError as e:
            issues.append(f'Cannot import imgsearch module: {e}')

        # Check base directory permissions
        try:
            self.base_dir.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            issues.append(f'Cannot create base directory {self.base_dir}. Permission denied.')

        return len(issues) == 0, issues

    def generate_systemd_service(self) -> str:
        """Generate systemd service file content."""
        python_exe = self.get_python_executable()
        base_dir = self.base_dir

        return f"""[Unit]
Description=ImgSearch - Lightweight Image Search Engine
After=network.target
Wants=network.target

[Service]
Type=simple
User={'root' if self.is_root else os.getenv('USER', 'nobody')}
Group={'root' if self.is_root else os.getenv('USER', 'nobody')}
WorkingDirectory={base_dir}
Environment=PYTHONPATH={os.path.dirname(os.path.dirname(__file__))}
Environment=PATH=/usr/local/bin:/usr/bin:/bin
ExecStart={python_exe} -m imgsearch service start -b {base_dir}
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=isearch

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths={base_dir}
PrivateTmp=true

# Resource limits
MemoryMax=2G
CPUQuota=80%

[Install]
WantedBy=multi-user.target
"""

    def generate_launchd_plist(self) -> str:
        """Generate macOS launchd plist file content."""
        python_exe = self.get_python_executable()
        base_dir = self.base_dir

        return f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.isearch.service</string>

    <key>ProgramArguments</key>
    <array>
        <string>{python_exe}</string>
        <string>-m</string>
        <string>imgsearch</string>
        <string>service</string>
        <string>start</string>
        <string>-b</string>
        <string>{base_dir}</string>
    </array>

    <key>WorkingDirectory</key>
    <string>{base_dir}</string>

    <key>EnvironmentVariables</key>
    <dict>
        <key>PYTHONPATH</key>
        <string>{os.path.dirname(os.path.dirname(__file__))}</string>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin</string>
    </dict>

    <key>RunAtLoad</key>
    <true/>

    <key>KeepAlive</key>
    <dict>
        <key>SuccessfulExit</key>
        <false/>
    </dict>

    <key>StandardOutPath</key>
    <string>{base_dir}/isearch.log</string>

    <key>StandardErrorPath</key>
    <string>{base_dir}/isearch.error.log</string>

    <key>ProcessType</key>
    <string>Background</string>

    <key>Nice</key>
    <integer>1</integer>
</dict>
</plist>
"""

    def install_service(self) -> bool:
        """
        Install isearch as a system service.

        Returns:
            bool: True if installation was successful, False otherwise
        """
        # Check prerequisites
        all_ok, issues = self.check_prerequisites()
        if not all_ok:
            print_err('Prerequisites check failed:')
            for issue in issues:
                print_err(f'  - {issue}')
            return False

        service_file_path = self.get_service_file_path()
        if not service_file_path:
            print_err('Cannot determine service file path for this system.')
            return False

        try:
            if self.system == 'linux':
                return self._install_systemd_service(service_file_path)
            elif self.system == 'darwin':
                return self._install_launchd_service(service_file_path)
        except Exception as e:
            print_err(f'Service installation failed: {e}')
            return False

        return False

    def _install_systemd_service(self, service_file_path: Path) -> bool:
        """Install systemd service."""
        print(f'Installing systemd service to {service_file_path}...')

        # Generate service file content
        service_content = self.generate_systemd_service()

        # Write service file (requires sudo for system directory)
        try:
            if self.is_root:
                # Running as root, write directly
                service_file_path.write_text(service_content)
            else:
                # Use sudo to write the file
                cmd = ['sudo', 'tee', str(service_file_path)]
                result = subprocess.run(cmd, input=service_content, text=True, capture_output=True)
                if result.returncode != 0:
                    print_err(f'Failed to write service file: {result.stderr}')
                    return False

            print(f'✓ Service file created: {service_file_path}')

            # Reload systemd daemon
            reload_cmd = ['sudo', 'systemctl', 'daemon-reload'] if not self.is_root else ['systemctl', 'daemon-reload']
            result = subprocess.run(reload_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print_warn(f'Failed to reload systemd daemon: {result.stderr}')
            else:
                print('✓ Systemd daemon reloaded')

            # Enable service
            enable_cmd = (
                ['sudo', 'systemctl', 'enable', 'isearch'] if not self.is_root else ['systemctl', 'enable', 'isearch']
            )
            result = subprocess.run(enable_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print_warn(f'Failed to enable service: {result.stderr}')
            else:
                print('✓ Service enabled for auto-start')

            print(colorize('✓ systemd service installation completed!', 'green', bold=True))
            print('\nYou can now manage the service with:')
            print('  sudo systemctl start isearch    # Start the service')
            print('  sudo systemctl stop isearch     # Stop the service')
            print('  sudo systemctl status isearch   # Check service status')

            return True

        except PermissionError:
            print_err(f'Permission denied writing to {service_file_path}')
            print_err('Try running with sudo or as root user.')
            return False
        except Exception as e:
            print_err(f'Failed to install systemd service: {e}')
            return False

    def _install_launchd_service(self, service_file_path: Path) -> bool:
        """Install launchd service."""
        print(f'Installing launchd service to {service_file_path}...')

        # Generate plist file content
        plist_content = self.generate_launchd_plist()

        try:
            # Ensure directory exists
            service_file_path.parent.mkdir(parents=True, exist_ok=True)

            # Write plist file
            service_file_path.write_text(plist_content)
            print(f'✓ Service file created: {service_file_path}')

            # Load the service
            result = subprocess.run(['launchctl', 'load', str(service_file_path)], capture_output=True, text=True)
            if result.returncode != 0:
                print_warn(f'Failed to load service: {result.stderr}')
            else:
                print('✓ Service loaded and enabled for auto-start')

            print(colorize('✓ launchd service installation completed!', 'green', bold=True))
            print('\nYou can now manage the service with:')
            print('  launchctl start com.isearch.service   # Start the service')
            print('  launchctl stop com.isearch.service    # Stop the service')
            print('  launchctl list | grep isearch         # Check service status')

            return True

        except PermissionError:
            print_err(f'Permission denied writing to {service_file_path}')
            return False
        except Exception as e:
            print_err(f'Failed to install launchd service: {e}')
            return False

    def uninstall_service(self) -> bool:
        """
        Uninstall isearch system service.

        Returns:
            bool: True if uninstallation was successful, False otherwise
        """
        service_file_path = self.get_service_file_path()
        if not service_file_path:
            print_err('Cannot determine service file path for this system.')
            return False

        if not service_file_path.exists():
            print_warn(f'Service file {service_file_path} does not exist. Service may not be installed.')
            return True

        try:
            if self.system == 'linux':
                return self._uninstall_systemd_service(service_file_path)
            elif self.system == 'darwin':
                return self._uninstall_launchd_service(service_file_path)
        except Exception as e:
            print_err(f'Service uninstallation failed: {e}')
            return False

        return False

    def _uninstall_systemd_service(self, service_file_path: Path) -> bool:
        """Uninstall systemd service."""
        print(f'Uninstalling systemd service from {service_file_path}...')

        try:
            # Stop the service if it's running
            stop_cmd = (
                ['sudo', 'systemctl', 'stop', 'isearch'] if not self.is_root else ['systemctl', 'stop', 'isearch']
            )
            result = subprocess.run(stop_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print('✓ Service stopped')
            else:
                print_warn(f'Failed to stop service (may not be running): {result.stderr}')

            # Disable the service
            disable_cmd = (
                ['sudo', 'systemctl', 'disable', 'isearch'] if not self.is_root else ['systemctl', 'disable', 'isearch']
            )
            result = subprocess.run(disable_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print('✓ Service disabled')
            else:
                print_warn(f'Failed to disable service: {result.stderr}')

            # Remove service file
            if self.is_root:
                service_file_path.unlink()
            else:
                result = subprocess.run(['sudo', 'rm', str(service_file_path)], capture_output=True, text=True)
                if result.returncode != 0:
                    print_err(f'Failed to remove service file: {result.stderr}')
                    return False

            print(f'✓ Service file removed: {service_file_path}')

            # Reload systemd daemon
            reload_cmd = ['sudo', 'systemctl', 'daemon-reload'] if not self.is_root else ['systemctl', 'daemon-reload']
            result = subprocess.run(reload_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print('✓ Systemd daemon reloaded')
            else:
                print_warn(f'Failed to reload systemd daemon: {result.stderr}')

            print(colorize('✓ systemd service uninstallation completed!', 'green', bold=True))
            return True

        except Exception as e:
            print_err(f'Failed to uninstall systemd service: {e}')
            return False

    def _uninstall_launchd_service(self, service_file_path: Path) -> bool:
        """Uninstall launchd service."""
        print(f'Uninstalling launchd service from {service_file_path}...')

        try:
            # Unload the service
            result = subprocess.run(['launchctl', 'unload', str(service_file_path)], capture_output=True, text=True)
            if result.returncode == 0:
                print('✓ Service unloaded')
            else:
                print_warn(f'Failed to unload service (may not be loaded): {result.stderr}')

            # Remove plist file
            service_file_path.unlink()
            print(f'✓ Service file removed: {service_file_path}')

            print(colorize('✓ launchd service uninstallation completed!', 'green', bold=True))
            return True

        except Exception as e:
            print_err(f'Failed to uninstall launchd service: {e}')
            return False

    def print_system_info(self):
        """Print system information for debugging."""
        print(colorize('System Information:', 'blue', bold=True))
        print(f'  OS: {platform.system()} {platform.release()}')
        print(f'  User: {"root" if self.is_root else "non-root"}')
        print(f'  Base Directory: {self.base_dir}')
        print(f'  Python: {self.get_python_executable()}')

        supported, error_msg = self.detect_system_support()
        if supported:
            print(f'  Service Support: {colorize("✓ Supported", "green")}')
        else:
            print(f'  Service Support: {colorize("✗ Not Supported", "red")} - {error_msg}')
