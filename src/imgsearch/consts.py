import os
from pathlib import Path

# The default model to use.
DEFAULT_MODEL = 'wkcn/TinyCLIP-ViT-8M-16-Text-3M-YFCC15M'
BATCH_SIZE = 50


def get_base_dir() -> Path:
    """Get base directory based on current user privileges."""
    if os.geteuid() == 0:  # root user
        return Path('/var/lib/isearch')
    else:
        return Path.home() / '.isearch'


# The paths for the database files.
BASE_DIR = get_base_dir()
DB_NAME = 'default'
IDX_NAME = 'index.db'
MAP_NAME = 'mapping.db'
CAPACITY = 10000

# The name for the service and the socket file.
SERVICE_NAME = 'isearch.service'
UNIX_SOCKET = BASE_DIR / 'isearch.sock'
