import os
from pathlib import Path

# The default model to use.
DEFAULT_MODEL = 'wkcn/TinyCLIP-ViT-8M-16-Text-3M-YFCC15M'
BATCH_SIZE = 50


def get_base_dir() -> Path:
    """Get base directory based on current user privileges."""
    if os.geteuid() == 0:  # root user
        return Path('/var/lib/isearch')
    else:
        return Path.home() / '.isearch'


# The paths for the database files.
BASE_DIR = get_base_dir()
DB_NAME = 'default'
IDX_NAME = 'index.db'
MAP_NAME = 'mapping.db'
CAPACITY = 10000

# The name for the service and the socket file.
SERVICE_NAME = 'isearch.service'
UNIX_SOCKET = BASE_DIR / 'isearch.sock'

# Service installation paths
SYSTEMD_SERVICE_PATH = '/etc/systemd/system/isearch.service'
LAUNCHD_PLIST_PATH_TEMPLATE = '~/Library/LaunchAgents/com.isearch.service.plist'
LAUNCHD_SERVICE_LABEL = 'com.isearch.service'

# Service file templates
SYSTEMD_SERVICE_TEMPLATE = """[Unit]
Description=ImgSearch - Lightweight Image Search Engine
After=network.target
Wants=network.target

[Service]
Type=simple
User={user}
Group={group}
WorkingDirectory={base_dir}
Environment=PYTHONPATH={pythonpath}
Environment=PATH=/usr/local/bin:/usr/bin:/bin
ExecStart={python_exe} -m imgsearch service start -b {base_dir}
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=isearch

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths={base_dir}
PrivateTmp=true

# Resource limits
MemoryMax=2G
CPUQuota=80%

[Install]
WantedBy=multi-user.target
"""

LAUNCHD_PLIST_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>{label}</string>

    <key>ProgramArguments</key>
    <array>
        <string>{python_exe}</string>
        <string>-m</string>
        <string>imgsearch</string>
        <string>service</string>
        <string>start</string>
        <string>-b</string>
        <string>{base_dir}</string>
    </array>

    <key>WorkingDirectory</key>
    <string>{base_dir}</string>

    <key>EnvironmentVariables</key>
    <dict>
        <key>PYTHONPATH</key>
        <string>{pythonpath}</string>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin</string>
    </dict>

    <key>RunAtLoad</key>
    <true/>

    <key>KeepAlive</key>
    <dict>
        <key>SuccessfulExit</key>
        <false/>
    </dict>

    <key>StandardOutPath</key>
    <string>{base_dir}/isearch.log</string>

    <key>StandardErrorPath</key>
    <string>{base_dir}/isearch.error.log</string>

    <key>ProcessType</key>
    <string>Background</string>

    <key>Nice</key>
    <integer>1</integer>
</dict>
</plist>
"""
